const request = require('supertest');
const express = require('express');
const itemsRouter = require('../routes/items');

// Create test app
const app = express();
app.use(express.json());
app.use('/api/items', itemsRouter);

// Add basic error handler
app.use((err, req, res, next) => {
  res.status(err.status || 500).json({ error: err.message });
});

describe('Items API', () => {
  describe('GET /api/items', () => {
    test('should return all items', async () => {
      const response = await request(app)
        .get('/api/items')
        .expect(200);

      // Test with real data from items.json
      expect(Array.isArray(response.body)).toBe(true);
      expect(response.body.length).toBeGreaterThan(0);

      // Check structure of first item
      expect(response.body[0]).toHaveProperty('id');
      expect(response.body[0]).toHaveProperty('name');
      expect(response.body[0]).toHaveProperty('category');
      expect(response.body[0]).toHaveProperty('price');
    });

    test('should filter items by search query', async () => {
      const response = await request(app)
        .get('/api/items?q=laptop')
        .expect(200);

      expect(Array.isArray(response.body)).toBe(true);
      // Should find items containing "laptop" (case insensitive)
      response.body.forEach(item => {
        expect(item.name.toLowerCase()).toContain('laptop');
      });
    });

    test('should limit number of items returned', async () => {
      const response = await request(app)
        .get('/api/items?limit=2')
        .expect(200);

      expect(Array.isArray(response.body)).toBe(true);
      expect(response.body.length).toBeLessThanOrEqual(2);
    });

    test('should combine search and limit', async () => {
      const response = await request(app)
        .get('/api/items?q=electronics&limit=1')
        .expect(200);

      expect(Array.isArray(response.body)).toBe(true);
      expect(response.body.length).toBeLessThanOrEqual(1);
    });
  });
    
    // test('should filter items by query', async () => {
    //   // Test implementation
    // });
  });

  describe('GET /api/items/:id', () => {
    test('should return item by id', async () => {
      const response = await request(app)
        .get('/api/items/1')
        .expect(200);

      expect(response.body).toHaveProperty('id', 1);
      expect(response.body).toHaveProperty('name');
      expect(response.body).toHaveProperty('category');
      expect(response.body).toHaveProperty('price');
    });

    test('should return 404 for non-existent item', async () => {
      const response = await request(app)
        .get('/api/items/99999')
        .expect(404);

      expect(response.body).toHaveProperty('error');
      expect(response.body.error).toBe('Item not found');
    });

    test('should handle invalid id format', async () => {
      const response = await request(app)
        .get('/api/items/invalid')
        .expect(404);

      // parseInt('invalid') returns NaN, so item won't be found
      expect(response.body).toHaveProperty('error');
    });
  });

  describe('POST /api/items', () => {
    test('should create new item', async () => {
      const newItem = {
        name: 'Test Item',
        category: 'Test Category',
        price: 100
      };

      const response = await request(app)
        .post('/api/items')
        .send(newItem)
        .expect(201);

      expect(response.body).toHaveProperty('id');
      expect(response.body.name).toBe(newItem.name);
      expect(response.body.category).toBe(newItem.category);
      expect(response.body.price).toBe(newItem.price);
    });

    test('should handle empty request body', async () => {
      const response = await request(app)
        .post('/api/items')
        .send({})
        .expect(201);

      // Current implementation doesn't validate, so it will create item with empty data
      expect(response.body).toHaveProperty('id');
    });
  });
});