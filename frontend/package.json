{"name": "frontend", "version": "2.0.0", "private": true, "dependencies": {"react": "^18.2.0", "react-dom": "^18.2.0", "react-router-dom": "^6.23.0", "react-scripts": "5.0.1"}, "scripts": {"start": "cross-env PORT=3000 react-scripts start", "build": "react-scripts build", "test": "react-scripts test"}, "browserslist": {"production": [">0.2%", "not dead", "not op_mini all"], "development": ["last 1 chrome version", "last 1 firefox version", "last 1 safari version"]}, "devDependencies": {"cross-env": "^7.0.3"}}